{
  "openapi": "3.0.0",
  "info": {
    "title": "WYLJ调试任务API",
    "version": "1.0.0",
    "description": "五羊轮胎调试任务相关接口文档"
  },
  "servers": [
    {
      "url": "http://your-domain.com",
      "description": "生产环境"
    }
  ],
  "paths": {
    "/roke/wylj/debug/task": {
      "post": {
        "summary": "开始调试任务",
        "description": "根据设备ID和工单ID开始调试任务，获取设备的开合模调试项当前值，并将工单状态更新为"调试中"",
        "tags": ["调试管理"],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["equipment_id", "work_order_id"],
                "properties": {
                  "equipment_id": {
                    "type": "integer",
                    "description": "设备ID",
                    "example": 123
                  },
                  "work_order_id": {
                    "type": "integer", 
                    "description": "工单ID",
                    "example": 456
                  }
                }
              },
              "examples": {
                "normal_request": {
                  "summary": "正常请求示例",
                  "value": {
                    "equipment_id": 123,
                    "work_order_id": 456
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "请求成功",
            "content": {
              "application/json": {
                "schema": {
                  "oneOf": [
                    {
                      "type": "object",
                      "properties": {
                        "state": {
                          "type": "string",
                          "enum": ["success"],
                          "description": "请求状态"
                        },
                        "msgs": {
                          "type": "string",
                          "description": "返回消息",
                          "example": "获取成功"
                        },
                        "data": {
                          "type": "number",
                          "description": "设备开合模调试项的当前值，从数采系统获取的实时数据"
                        }
                      },
                      "required": ["state", "msgs", "data"]
                    },
                    {
                      "type": "object", 
                      "properties": {
                        "state": {
                          "type": "string",
                          "enum": ["error"],
                          "description": "请求状态"
                        },
                        "msgs": {
                          "type": "string",
                          "description": "错误消息"
                        }
                      },
                      "required": ["state", "msgs"]
                    }
                  ]
                },
                "examples": {
                  "success_response": {
                    "summary": "成功响应示例",
                    "value": {
                      "state": "success",
                      "msgs": "获取成功", 
                      "data": 25.6
                    }
                  },
                  "missing_params_error": {
                    "summary": "缺少参数错误",
                    "value": {
                      "state": "error",
                      "msgs": "设备id和工单id不能为空"
                    }
                  },
                  "equipment_not_found": {
                    "summary": "设备不存在错误",
                    "value": {
                      "state": "error", 
                      "msgs": "设备不存在"
                    }
                  },
                  "no_debug_item": {
                    "summary": "设备未设置调试项错误",
                    "value": {
                      "state": "error",
                      "msgs": "设备未设置开合模调试项"
                    }
                  },
                  "work_order_not_found": {
                    "summary": "工单不存在错误", 
                    "value": {
                      "state": "error",
                      "msgs": "工单不存在"
                    }
                  }
                }
              }
            }
          }
        },
        "security": [
          {
            "sessionAuth": []
          }
        ]
      },
      "options": {
        "summary": "CORS预检请求",
        "description": "处理跨域预检请求",
        "tags": ["调试管理"],
        "responses": {
          "200": {
            "description": "CORS预检成功"
          }
        }
      }
    }
  },
  "components": {
    "securitySchemes": {
      "sessionAuth": {
        "type": "apiKey",
        "in": "cookie",
        "name": "session_id",
        "description": "Odoo会话认证"
      }
    }
  }
}
