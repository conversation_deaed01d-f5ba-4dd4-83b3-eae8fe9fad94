{"swagger": "2.0", "info": {"title": "WYLJ调试任务API", "version": "1.0.0", "description": "五羊轮胎调试任务相关接口文档"}, "host": "your-domain.com", "basePath": "/", "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "tags": [{"name": "调试管理", "description": "设备调试相关接口"}], "paths": {"/roke/wylj/debug/task": {"post": {"tags": ["调试管理"], "summary": "开始调试任务", "description": "根据设备ID和工单ID开始调试任务，获取设备的开合模调试项当前值，并将工单状态更新为调试中", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DebugTaskRequest"}}], "responses": {"200": {"description": "请求成功", "schema": {"oneOf": [{"$ref": "#/definitions/SuccessResponse"}, {"$ref": "#/definitions/ErrorResponse"}]}}}, "security": [{"sessionAuth": []}]}, "options": {"tags": ["调试管理"], "summary": "CORS预检请求", "description": "处理跨域预检请求", "responses": {"200": {"description": "CORS预检成功"}}}}}, "definitions": {"DebugTaskRequest": {"type": "object", "required": ["equipment_id", "work_order_id"], "properties": {"equipment_id": {"type": "integer", "description": "设备ID", "example": 123}, "work_order_id": {"type": "integer", "description": "工单ID", "example": 456}}}, "SuccessResponse": {"type": "object", "required": ["state", "msgs", "data"], "properties": {"state": {"type": "string", "enum": ["success"], "description": "请求状态"}, "msgs": {"type": "string", "description": "返回消息", "example": "获取成功"}, "data": {"type": "number", "description": "设备开合模调试项的当前值，从数采系统获取的实时数据", "example": 25.6}}}, "ErrorResponse": {"type": "object", "required": ["state", "msgs"], "properties": {"state": {"type": "string", "enum": ["error"], "description": "请求状态"}, "msgs": {"type": "string", "description": "错误消息"}}}}, "securityDefinitions": {"sessionAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "<PERSON><PERSON>", "description": "Odoo会话认证，格式: session_id=your_session_id"}}}