{"openapi": "3.0.0", "info": {"title": "WYLJ调试任务API", "version": "1.0.0", "description": "五羊轮胎调试任务相关接口文档"}, "servers": [{"url": "http://your-domain.com", "description": "生产环境"}], "paths": {"/roke/wylj/debug/task": {"post": {"summary": "开始调试任务", "description": "根据设备ID和工单ID开始调试任务，获取设备的开合模调试项当前值，并将工单状态更新为调试中", "tags": ["调试管理"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DebugTaskRequest"}}}}, "responses": {"200": {"description": "请求成功", "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"$ref": "#/components/schemas/ErrorResponse"}]}}}}}, "security": [{"sessionAuth": []}]}, "options": {"summary": "CORS预检请求", "description": "处理跨域预检请求", "tags": ["调试管理"], "responses": {"200": {"description": "CORS预检成功"}}}}}, "components": {"schemas": {"DebugTaskRequest": {"type": "object", "required": ["equipment_id", "work_order_id"], "properties": {"equipment_id": {"type": "integer", "description": "设备ID", "example": 123}, "work_order_id": {"type": "integer", "description": "工单ID", "example": 456}}}, "SuccessResponse": {"type": "object", "required": ["state", "msgs", "data"], "properties": {"state": {"type": "string", "enum": ["success"], "description": "请求状态"}, "msgs": {"type": "string", "description": "返回消息", "example": "获取成功"}, "data": {"type": "number", "description": "设备开合模调试项的当前值，从数采系统获取的实时数据", "example": 25.6}}}, "ErrorResponse": {"type": "object", "required": ["state", "msgs"], "properties": {"state": {"type": "string", "enum": ["error"], "description": "请求状态"}, "msgs": {"type": "string", "description": "错误消息", "enum": ["设备id和工单id不能为空", "设备不存在", "设备未设置开合模调试项", "工单不存在"]}}}}, "securitySchemes": {"sessionAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "session_id", "description": "Odoo会话认证"}}}}