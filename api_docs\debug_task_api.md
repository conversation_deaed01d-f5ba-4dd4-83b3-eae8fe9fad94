# WYLJ调试任务API文档

## 接口概述

**接口名称**: 开始调试任务  
**接口路径**: `/roke/wylj/debug/task`  
**请求方法**: `POST`  
**内容类型**: `application/json`  
**认证方式**: Odoo用户会话认证  

## 功能描述

该接口用于开始设备调试任务，主要功能包括：
1. 验证设备和工单的有效性
2. 检查设备是否配置了开合模调试项
3. 从数采系统获取设备调试项的当前实时数据
4. 将工单状态更新为"调试中"

## 请求参数

### 请求头
```
Content-Type: application/json
Cookie: session_id=<odoo_session_id>
```

### 请求体
```json
{
  "equipment_id": 123,
  "work_order_id": 456
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| equipment_id | integer | 是 | 设备ID，对应roke.mes.equipment模型的记录ID |
| work_order_id | integer | 是 | 工单ID，对应roke.work.order模型的记录ID |

## 响应格式

### 成功响应
```json
{
  "state": "success",
  "msgs": "获取成功",
  "data": 25.6
}
```

| 字段名 | 类型 | 描述 |
|--------|------|------|
| state | string | 请求状态，成功时为"success" |
| msgs | string | 返回消息 |
| data | number | 设备开合模调试项的当前值，从数采系统实时获取 |

### 错误响应
```json
{
  "state": "error",
  "msgs": "错误描述信息"
}
```

| 字段名 | 类型 | 描述 |
|--------|------|------|
| state | string | 请求状态，错误时为"error" |
| msgs | string | 具体的错误信息 |

## 错误码说明

| 错误信息 | 描述 | 解决方案 |
|----------|------|----------|
| 设备id和工单id不能为空 | 请求参数缺失 | 确保equipment_id和work_order_id都有值 |
| 设备不存在 | 指定的设备ID在系统中不存在 | 检查设备ID是否正确 |
| 设备未设置开合模调试项 | 设备的item_line_id字段为空 | 需要先为设备配置开合模调试项 |
| 工单不存在 | 指定的工单ID在系统中不存在 | 检查工单ID是否正确 |

## 业务逻辑说明

### 数据获取逻辑
1. 接口会根据设备的`apikey`和调试项的`item_id`从数采系统获取实时数据
2. 数采系统地址：`http://************:83/go/api/getdata`
3. 仅在内网环境（base_url包含************）时才会调用数采系统
4. 非内网环境返回固定值"暂无"

### 状态更新
- 调用成功后，工单状态会自动更新为"调试中"
- 工单状态字段：`roke.work.order.state`

## 请求示例

### cURL示例
```bash
curl -X POST "http://your-domain.com/roke/wylj/debug/task" \
  -H "Content-Type: application/json" \
  -H "Cookie: session_id=your_session_id" \
  -d '{
    "equipment_id": 123,
    "work_order_id": 456
  }'
```

### JavaScript示例
```javascript
fetch('/roke/wylj/debug/task', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include', // 包含cookie
  body: JSON.stringify({
    equipment_id: 123,
    work_order_id: 456
  })
})
.then(response => response.json())
.then(data => {
  if (data.state === 'success') {
    console.log('调试项当前值:', data.data);
  } else {
    console.error('错误:', data.msgs);
  }
});
```

### Python示例
```python
import requests

url = "http://your-domain.com/roke/wylj/debug/task"
headers = {
    "Content-Type": "application/json"
}
data = {
    "equipment_id": 123,
    "work_order_id": 456
}

response = requests.post(url, json=data, headers=headers, cookies={"session_id": "your_session_id"})
result = response.json()

if result["state"] == "success":
    print(f"调试项当前值: {result['data']}")
else:
    print(f"错误: {result['msgs']}")
```

## 相关模型说明

### roke.mes.equipment (设备模型)
- `id`: 设备ID
- `apikey`: 数采系统API密钥
- `item_line_id`: 开合模调试项配置

### roke.work.order (工单模型)  
- `id`: 工单ID
- `state`: 工单状态，可能值包括：未完工、已完工、强制完工、暂停、调试中、调试完成、自检完成

### roke.mes.equipment.item (设备检查项模型)
- `item_id`: 检查项ID，用于数采系统查询
- `item_name`: 检查项名称

## 注意事项

1. **认证要求**: 必须使用有效的Odoo用户会话
2. **网络环境**: 数采功能仅在内网环境可用
3. **CORS支持**: 接口支持跨域请求，设置了`cors='*'`
4. **CSRF**: 接口已禁用CSRF验证（`csrf=False`）
5. **超时设置**: 数采系统请求超时时间为10秒

## 版本信息

- **API版本**: 1.0.0
- **最后更新**: 2024-06-24
- **维护团队**: WYLJ开发团队
