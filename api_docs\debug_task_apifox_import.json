{"apifoxCollection": {"info": {"name": "WYLJ调试任务API", "description": "五羊轮胎调试任务相关接口文档", "version": "1.0.0"}, "item": [{"name": "调试管理", "item": [{"name": "开始调试任务", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/wylj/debug/task", "host": ["{{baseUrl}}"], "path": ["roke", "wylj", "debug", "task"]}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "session_id={{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"equipment_id\": 123,\n  \"work_order_id\": 456\n}", "options": {"raw": {"language": "json"}}}, "description": "根据设备ID和工单ID开始调试任务，获取设备的开合模调试项当前值，并将工单状态更新为调试中"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/wylj/debug/task", "host": ["{{baseUrl}}"], "path": ["roke", "wylj", "debug", "task"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"equipment_id\": 123,\n  \"work_order_id\": 456\n}"}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"获取成功\",\n  \"data\": 25.6\n}"}, {"name": "参数错误", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/wylj/debug/task", "host": ["{{baseUrl}}"], "path": ["roke", "wylj", "debug", "task"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"equipment_id\": null,\n  \"work_order_id\": 456\n}"}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"设备id和工单id不能为空\"\n}"}, {"name": "设备不存在", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/wylj/debug/task", "host": ["{{baseUrl}}"], "path": ["roke", "wylj", "debug", "task"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"equipment_id\": 999999,\n  \"work_order_id\": 456\n}"}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"设备不存在\"\n}"}]}]}], "variable": [{"key": "baseUrl", "value": "http://your-domain.com", "type": "string"}, {"key": "session_id", "value": "your_session_id_here", "type": "string"}]}}